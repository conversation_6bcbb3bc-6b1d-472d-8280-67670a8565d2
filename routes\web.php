<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\VisitorsController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

Route::get('/', function () {
    return view('index');
});



// Admin Dashboard
Route::get('/admin-dashboard', function () {
    return view('admin.index');
})->middleware(['auth', 'verified', 'role:admin'])->name('admin.dashboard');

// User Dashboard
Route::get('/user/dashboard', function () {
    return view('user.dashboard');
})->middleware(['auth', 'verified', 'role:user'])->name('user.dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin Category Management Routes
Route::middleware(['auth', 'verified', 'role:admin'])->group(function () {
    Route::get('/add-category', [VisitorsController::class, 'add_category'])->name('add.category');
    Route::post('/add-category', [VisitorsController::class, 'store_category'])->name('store.category');
    Route::get('/all-categories', [VisitorsController::class, 'all_categories'])->name('all.categories');
});

require __DIR__.'/auth.php';

Route::controller(VisitorsController::class)->group(function(){
    Route::get('/courses', 'courses')->name('visitors.courses');
    Route::get('/course/{id}', 'course')->name('visitors.course');

    Route::get('/about', 'about')->name('visitors.about');
    Route::get('/logout', 'destroy')->name('visitors.logout');


});

// Admin Courses Routes
Route::middleware(['auth', 'verified', 'role:admin'])->group(function () {
    // Courses Management
    Route::get('/admin-courses', [\App\Http\Controllers\Admin\CourseController::class, 'index'])->name('admin.courses.index');
    Route::get('/admin-courses-create', [\App\Http\Controllers\Admin\CourseController::class, 'create'])->name('admin.courses.create');
    Route::get('/admin-courses-debug', [\App\Http\Controllers\Admin\CourseController::class, 'debug'])->name('admin.courses.debug');
    Route::post('/admin/courses-store', [\App\Http\Controllers\Admin\CourseController::class, 'store'])->name('admin.courses.store');
    Route::get('/admin/courses-show/{id}', [\App\Http\Controllers\Admin\CourseController::class, 'show'])->name('admin.courses.show');
    Route::get('/admin/courses-edit/{id}', [\App\Http\Controllers\Admin\CourseController::class, 'edit'])->name('admin.courses.edit');
    Route::put('/admin/courses-update/{id}', [\App\Http\Controllers\Admin\CourseController::class, 'update'])->name('admin.courses.update');
    Route::delete('/admin/courses-delete/{id}', [\App\Http\Controllers\Admin\CourseController::class, 'destroy'])->name('admin.courses.destroy');
    Route::post('/admin/courses-bulk-action', [\App\Http\Controllers\Admin\CourseController::class, 'bulkAction'])->name('admin.courses.bulk-action');

    // AJAX endpoints for inline creation
    Route::post('/admin/courses/ajax/create-category', [\App\Http\Controllers\Admin\CourseController::class, 'createCategoryAjax'])->name('admin.courses.ajax.create-category');
    Route::post('/admin/courses/ajax/create-instructor', [\App\Http\Controllers\Admin\CourseController::class, 'createInstructorAjax'])->name('admin.courses.ajax.create-instructor');
});

// Admin CMS Routes
Route::middleware(['auth', 'verified', 'role:admin'])->prefix('admin/cms')->name('admin.cms.')->group(function () {
    // Pages
    Route::resource('pages', \App\Http\Controllers\Admin\CMS\PageController::class);

    // Posts
    Route::resource('posts', \App\Http\Controllers\Admin\CMS\PostController::class);

    // Categories
    Route::resource('categories', \App\Http\Controllers\Admin\CMS\CategoryController::class);

    // Tags
    Route::resource('tags', \App\Http\Controllers\Admin\CMS\TagController::class);

    // Media
    Route::resource('media', \App\Http\Controllers\Admin\CMS\MediaController::class);
    Route::post('media/upload', [\App\Http\Controllers\Admin\CMS\MediaController::class, 'upload'])->name('media.upload');

    // Menus
    Route::resource('menus', \App\Http\Controllers\Admin\CMS\MenuController::class);

    // Site Settings
    Route::get('settings', [\App\Http\Controllers\Admin\CMS\SiteSettingController::class, 'index'])->name('settings.index');
    Route::post('settings', [\App\Http\Controllers\Admin\CMS\SiteSettingController::class, 'update'])->name('settings.update');
});

Route::get('/test-view', function () {
    return view('student.dashboard');
});

// Debug route to check user authentication and role
Route::get('/debug-user', function () {
    if (Auth::check()) {
        $user = Auth::user();
        return response()->json([
            'authenticated' => true,
            'user_id' => $user->id,
            'user_name' => $user->name,
            'user_email' => $user->email,
            'user_role' => $user->role,
            'is_admin' => $user->isAdmin(),
            'is_user' => $user->isUser(),
            'dashboard_route' => $user->getDashboardRoute(),
        ]);
    } else {
        return response()->json([
            'authenticated' => false,
            'message' => 'User not authenticated'
        ]);
    }
})->middleware('auth');




